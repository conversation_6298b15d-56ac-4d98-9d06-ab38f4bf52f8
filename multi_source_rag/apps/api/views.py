"""
Views for the API app.
"""

from apps.documents.models import DocumentSource, RawDocument
from apps.search.models import Conversation, SearchQuery
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.http import require_POST
from rest_framework import permissions, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from .models import APIKey


class DocumentViewSet(viewsets.ViewSet):
    """
    API endpoint for documents.
    """

    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """List documents."""
        try:
            from apps.documents.models import RawDocument

            # Get query parameters
            limit = int(request.query_params.get('limit', 20))
            offset = int(request.query_params.get('offset', 0))
            source_type = request.query_params.get('source_type')

            # Build queryset
            queryset = RawDocument.objects.select_related('source').order_by('-created_at')

            if source_type:
                queryset = queryset.filter(source__source_type=source_type)

            # Apply pagination
            total_count = queryset.count()
            documents = queryset[offset:offset + limit]

            # Serialize documents
            document_data = []
            for doc in documents:
                document_data.append({
                    'id': doc.id,
                    'external_id': doc.external_id,
                    'title': doc.title,
                    'content_type': doc.content_type,
                    'source_type': doc.source.source_type,
                    'source_name': doc.source.name,
                    'created_at': doc.created_at.isoformat(),
                    'metadata': doc.metadata
                })

            return Response({
                "status": "success",
                "data": {
                    "documents": document_data,
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset
                }
            })

        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Failed to list documents: {str(e)}"
            }, status=500)

    def retrieve(self, request, pk=None):
        """Retrieve a document."""
        try:
            from apps.documents.models import RawDocument

            document = RawDocument.objects.select_related('source').get(id=pk)

            # Get document content if available
            content = ""
            if hasattr(document, 'document_content'):
                content = document.document_content.content

            document_data = {
                'id': document.id,
                'external_id': document.external_id,
                'title': document.title,
                'content': content,
                'content_type': document.content_type,
                'source_type': document.source.source_type,
                'source_name': document.source.name,
                'created_at': document.created_at.isoformat(),
                'metadata': document.metadata
            }

            return Response({
                "status": "success",
                "data": document_data
            })

        except RawDocument.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Document not found"
            }, status=404)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Failed to retrieve document: {str(e)}"
            }, status=500)


class DocumentSourceViewSet(viewsets.ViewSet):
    """
    API endpoint for document sources.
    """

    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """List document sources."""
        try:
            from apps.documents.models import DocumentSource

            # Get query parameters
            limit = int(request.query_params.get('limit', 20))
            offset = int(request.query_params.get('offset', 0))
            source_type = request.query_params.get('source_type')

            # Build queryset
            queryset = DocumentSource.objects.order_by('-created_at')

            if source_type:
                queryset = queryset.filter(source_type=source_type)

            # Apply pagination
            total_count = queryset.count()
            sources = queryset[offset:offset + limit]

            # Serialize sources
            source_data = []
            for source in sources:
                source_data.append({
                    'id': source.id,
                    'name': source.name,
                    'source_type': source.source_type,
                    'is_active': source.is_active,
                    'last_synced': source.last_synced.isoformat() if source.last_synced else None,
                    'created_at': source.created_at.isoformat(),
                    'config': source.config
                })

            return Response({
                "status": "success",
                "data": {
                    "sources": source_data,
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset
                }
            })

        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Failed to list sources: {str(e)}"
            }, status=500)

    def retrieve(self, request, pk=None):
        """Retrieve a document source."""
        # TODO: Implement source retrieval
        return Response({"status": "success"})


class SearchViewSet(viewsets.ViewSet):
    """
    API endpoint for search.
    """

    permission_classes = [permissions.IsAuthenticated]

    def create(self, request):
        """
        Perform a search using RAG.

        Request body:
        {
            "query": "What is the meaning of life?",
            "top_k": 5,
            "tenant_slug": "stride",
            "filter": {"source_type": "slack"}
        }

        Returns:
        {
            "status": "success",
            "data": {
                "query": "What is the meaning of life?",
                "answer": "The meaning of life is...",
                "timestamp": "2023-01-01T00:00:00Z",
                "metrics": {
                    "retriever_score": 0.85,
                    "confidence_score": 0.9,
                    "processing_time": "0.25s"
                },
                "sources": [
                    {
                        "id": 1,
                        "text": "The meaning of life is...",
                        "relevance": 0.9,
                        "metadata": {
                            "title": "Philosophy 101",
                            "url": "https://example.com/philosophy",
                            "source": "web",
                            "created_at": "2023-01-01T00:00:00Z"
                        }
                    }
                ]
            }
        }
        """
        import time
        import logging
        from apps.search.services.rag_service import RAGService

        from .serializers import (SearchQuerySerializer,
                                  SearchRequestSerializer,
                                  SearchResultSerializer)

        logger = logging.getLogger(__name__)

        # Validate request
        serializer = SearchRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"status": "error", "message": "Invalid request parameters", "details": serializer.errors}, status=400)

        # Extract parameters
        query = serializer.validated_data["query"]
        top_k = serializer.validated_data.get("top_k", 20)
        tenant_slug = serializer.validated_data.get("tenant_slug")
        filter_params = serializer.validated_data.get("filter")

        # Create RAG service using the new consolidated RAGService
        # This uses the consolidated service that combines all previous functionality
        rag_service = RAGService(
            user=request.user,
            tenant_slug=tenant_slug
        )

        # Perform search
        try:
            # Extract advanced search parameters
            use_hybrid_search = serializer.validated_data.get("use_hybrid_search", True)
            use_context_aware = serializer.validated_data.get("use_context_aware", True)
            use_query_expansion = serializer.validated_data.get(
                "use_query_expansion", False
            )
            use_multi_step_reasoning = serializer.validated_data.get(
                "use_multi_step_reasoning", False
            )
            output_format = serializer.validated_data.get("output_format", "text")
            # Get minimum relevance score from request or use default
            min_relevance_score = serializer.validated_data.get("min_relevance_score", 0.4)

            # Use lower threshold for specific queries
            query_lower = query.lower()
            # Only override if the user didn't explicitly set a value
            if "min_relevance_score" not in serializer.validated_data:
                if 'curana' in query_lower and 'summarize' in query_lower:
                    min_relevance_score = 0.2  # Very low threshold for summarizing Curana conversations
                elif 'customer feedback' in query_lower or 'customer feedbacks' in query_lower:
                    min_relevance_score = 0.2  # Very low threshold for customer feedback queries
                elif 'curana' in query_lower or 'summarize' in query_lower:
                    min_relevance_score = 0.3  # Lower threshold for Curana-related or summarization queries

            # Start timer for processing time measurement
            start_time = time.time()

            # Execute search using the working RAGService interface
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=top_k,
                metadata_filter=filter_params,
                output_format=output_format,
                min_relevance_score=min_relevance_score,
                use_hybrid_search=use_hybrid_search,
                use_context_aware=use_context_aware,
                use_query_expansion=use_query_expansion,
                use_multi_step_reasoning=use_multi_step_reasoning,
            )

            # Calculate processing time
            processing_time = time.time() - start_time

            # Get citations from the search result with optimized loading
            citations = search_result.citations.select_related(
                'document_chunk__document__source'
            ).order_by('rank')

            # Format sources in a cleaner way
            sources = []
            for citation in citations:
                # Get document chunk and its metadata
                chunk = citation.document_chunk
                source = {
                    "id": citation.id,
                    "text": chunk.text[:300] + ("..." if len(chunk.text) > 300 else ""),  # Truncate long text
                    "relevance": round(citation.relevance_score, 3),
                    "metadata": {}
                }

                # Add metadata if available
                if chunk.document:
                    source["metadata"]["title"] = chunk.document.title or "Untitled Document"
                    source["metadata"]["url"] = chunk.document.permalink

                    if chunk.document.source:
                        source["metadata"]["source"] = chunk.document.source.source_type

                    if chunk.document.created_at:
                        source["metadata"]["created_at"] = chunk.document.created_at.isoformat()

                sources.append(source)

            # Check if this is a fallback answer
            is_fallback = False
            if len(sources) == 0 and "while i couldn't find specific information about" in search_result.generated_answer.lower():
                is_fallback = True
                logger.info(f"Using fallback answer for query: '{query}'")

            # Create response object
            response_data = {
                "status": "success",
                "data": {
                    "query": query,
                    "answer": search_result.generated_answer,
                    "timestamp": search_result.timestamp.isoformat(),
                    "metrics": {
                        "retriever_score": round(search_result.retriever_score_avg, 3),
                        "confidence_score": round(search_result.llm_confidence_score, 3),
                        "processing_time": f"{processing_time:.2f}s",
                        "sources_count": len(sources),
                        "is_fallback": is_fallback
                    },
                    "sources": sources
                }
            }

            # Return the formatted response
            return Response(response_data)
        except ValueError as e:
            logger.warning(f"Value error in search API: {str(e)}")
            return Response({"status": "error", "message": str(e), "error_type": "value_error"}, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in search API: {str(e)}")
            return Response({"status": "error", "message": "An unexpected error occurred", "error_type": "server_error", "details": str(e)}, status=500)


class ConversationViewSet(viewsets.ViewSet):
    """
    API endpoint for conversations.
    """

    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """List conversations."""
        # TODO: Implement conversation listing
        return Response({"status": "success"})

    def retrieve(self, request, pk=None):
        """Retrieve a conversation."""
        # TODO: Implement conversation retrieval
        return Response({"status": "success"})

    @action(detail=True, methods=["post"])
    def add_message(self, request, pk=None):
        """Add a message to a conversation."""
        # TODO: Implement message addition
        return Response({"status": "success"})


@login_required
def api_key_list(request):
    """View for listing API keys."""
    # TODO: Implement API key listing
    return JsonResponse({"status": "success"})


@login_required
@require_POST
def create_api_key(request):
    """View for creating an API key."""
    # TODO: Implement API key creation
    return JsonResponse({"status": "success"})


@login_required
@require_POST
def revoke_api_key(request, key_id):
    """View for revoking an API key."""
    # TODO: Implement API key revocation
    return JsonResponse({"status": "success"})
