"""
Consolidated factory for document source interfaces.

This factory handles both local and remote data sources intelligently,
eliminating the need for separate factory classes.
"""

from typing import Any, Dict, Optional

from .base import DocumentSourceInterface
from .file import FileSourceInterface
from .local_slack import LocalSlackSourceInterface
from .slack import SlackSourceInterface


class DocumentSourceFactory:
    """
    Consolidated factory for creating document source interfaces.

    This factory intelligently handles both local and remote sources,
    supporting local data processing when specified.
    """

    @staticmethod
    def create_source(
        source_type: str,
        config: Optional[Dict[str, Any]] = None,
        prefer_local: bool = False
    ) -> DocumentSourceInterface:
        """
        Create a document source interface.

        Args:
            source_type: Type of source to create (e.g., 'slack', 'local_slack', 'github', 'file')
            config: Configuration for the source
            prefer_local: If True, prefer local interfaces when available

        Returns:
            DocumentSourceInterface: Document source interface

        Raises:
            ValueError: If the source type is not supported
        """
        config = config or {}

        # Handle local source types (strip 'local_' prefix)
        if source_type.startswith("local_"):
            actual_source_type = source_type.replace("local_", "")
            prefer_local = True
        else:
            actual_source_type = source_type

        if actual_source_type == "file":
            return FileSourceInterface(config)
        elif actual_source_type == "slack":
            # Use local Slack interface if prefer_local is True or if explicitly requested
            if prefer_local or source_type == "local_slack":
                return LocalSlackSourceInterface(config)
            else:
                return SlackSourceInterface(config)
        elif actual_source_type == "github":
            from .github import GitHubInterface
            return GitHubInterface(config)
        else:
            raise ValueError(f"Unsupported source type: {source_type}")

    @staticmethod
    def get_supported_types() -> list[str]:
        """
        Get list of supported source types.

        Returns:
            List of supported source type strings
        """
        return [
            "file",
            "slack",
            "local_slack",
            "github"
        ]
