"""
Django management command to ingest Slack data with thread merging.

This command ingests Slack data from local files, merges messages into more meaningful chunks,
and stores them in the database.
"""

import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

from apps.accounts.models import Tenant
from apps.documents.models import (ChunkRelationship, DocumentChunk,
                                   DocumentSource, RawDocument)
from apps.documents.services.local_ingestion_service import \
    LocalIngestionService
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand
from django.db import transaction

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Ingest Slack data with thread merging"

    def add_arguments(self, parser):
        parser.add_argument(
            "--channel",
            type=str,
            default="1-productengineering",
            help="Channel ID to ingest",
        )
        parser.add_argument(
            "--data-dir",
            type=str,
            default="../data",
            help="Directory containing Slack data files",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=50,
            help="Number of documents to process in a batch",
        )
        parser.add_argument(
            "--clean",
            action="store_true",
            help="Clean existing documents before ingestion",
        )
        parser.add_argument(
            "--tenant", type=str, default=None, help="Tenant slug to use"
        )
        parser.add_argument(
            "--limit",
            type=int,
            default=None,
            help="Limit the number of documents to ingest",
        )

    def setup_environment(self):
        """
        Set up the environment.

        Returns:
            Tuple[Tenant, DocumentSource]: Tuple of tenant and document source
        """
        # Get existing tenant or use the first one
        try:
            if self.tenant_slug:
                tenant = Tenant.objects.get(slug=self.tenant_slug)
            else:
                tenant = Tenant.objects.first()

            if not tenant:
                # Create a new tenant if none exists
                tenant = Tenant.objects.create(
                    name="Default Tenant", domain="default.com", slug="default"
                )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error getting tenant: {str(e)}"))
            raise

        # Get or create document source
        try:
            source = DocumentSource.objects.get(
                tenant=tenant, name=f"Slack {self.channel}"
            )
            # Update config
            source.config = {
                "channel": self.channel,
                "data_dir": self.data_dir,
                "use_thread_merging": True,
            }
            source.save()
        except DocumentSource.DoesNotExist:
            source = DocumentSource.objects.create(
                tenant=tenant,
                name=f"Slack {self.channel}",
                source_type="slack",
                config={
                    "channel": self.channel,
                    "data_dir": self.data_dir,
                    "use_thread_merging": True,
                },
                is_active=True,
            )

        return tenant, source

    def handle(self, *args, **options):
        """
        Execute the command.
        """
        # Get options
        self.channel = options["channel"]
        self.data_dir = options["data_dir"]
        self.batch_size = options["batch_size"]
        self.clean = options["clean"]
        self.tenant_slug = options["tenant"]
        self.limit = options["limit"]

        self.stdout.write(
            self.style.SUCCESS(
                f"Ingesting Slack data for channel {self.channel} with thread merging..."
            )
        )

        # Set up environment
        tenant, source = self.setup_environment()

        # Create ingestion service
        ingestion_service = LocalIngestionService(tenant)

        # Process source
        with transaction.atomic():
            # Delete existing documents for clean test if requested
            if self.clean:
                self.stdout.write(
                    self.style.WARNING(
                        f"Cleaning existing documents for source {source.name}..."
                    )
                )
                RawDocument.objects.filter(source=source).delete()

            # Process source
            processed, failed = ingestion_service.process_source(
                source=source,
                batch_size=self.batch_size,
                data_dir=self.data_dir,
                limit=self.limit,
            )

        # Print results
        self.stdout.write(
            self.style.SUCCESS(f"Processed {processed} documents, failed {failed}")
        )

        # Check results
        documents = RawDocument.objects.filter(source=source)
        self.stdout.write(f"Total documents in database: {documents.count()}")

        chunks = DocumentChunk.objects.filter(document__source=source)
        self.stdout.write(f"Total chunks in database: {chunks.count()}")

        relationships = ChunkRelationship.objects.filter(
            source_chunk__document__source=source
        )
        self.stdout.write(
            f"Total chunk relationships in database: {relationships.count()}"
        )

        # Check thread relationships
        thread_relationships = ChunkRelationship.objects.filter(
            source_chunk__document__source=source,
            relationship_type__in=["parent", "child"],
        )
        self.stdout.write(
            f"Thread relationships in database: {thread_relationships.count()}"
        )

        self.stdout.write(self.style.SUCCESS("Ingestion completed"))
